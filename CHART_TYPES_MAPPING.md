# CD2GUI 图表类型映射

## 支持的图表类型

根据Chart Components.txt文件，cd2gui现在支持以下所有图表类型：

### 1. BarChart
- **模板文件**: `bar-chart.ftl`
- **组件**: `GemBarChart`
- **数据类型**: `GemBarChartData`
- **参数**: `data`, `displayValue`, `stacked`, `maxValue`, `minValue`
- **用途**: 展示分类数据的数量对比，支持堆叠显示

### 2. BulletChart
- **模板文件**: `bullet-chart.ftl`
- **组件**: `GemBulletChart`
- **参数**: `value`, `reference`, `thresholdvalue`, `title`, `subtitle`, `range`, `steps`
- **用途**: 显示单一度量值与目标值的对比，适合KPI展示

### 3. CandlestickChart
- **模板文件**: `candlestick-chart.ftl`
- **组件**: `GemCandlestickChart`
- **数据类型**: `List<GemData2CandlestickChart>`
- **参数**: `data`
- **用途**: 显示时间序列数据，常用于股价等金融数据

### 4. GaugeChart
- **模板文件**: `gauge-chart.ftl`
- **组件**: `GemGaugeChart`
- **数据类型**: `GemGaugeChartData`
- **参数**: `data`, `min`, `max`, `unit`, `textValue`
- **用途**: 仪表盘显示，适合展示单一数值在范围内的位置

### 5. HeatmapChart
- **模板文件**: `heatmap-chart.ftl`
- **组件**: `GemHeatmapChart`
- **数据类型**: `GemHeatmapChartData`
- **参数**: `data`, `xLabel`, `yLabel`, `timestampdata`
- **用途**: 热力图显示，适合展示二维数据的密度分布

### 6. LineChart
- **模板文件**: `line-chart.ftl`
- **组件**: `GemLineChart`
- **数据类型**: `GemLineChartData`
- **参数**: `data`, `displayValue`, `enableBackgroundColor`, `maxValue`, `minValue`
- **用途**: 折线图，适合展示数据随时间的变化趋势

### 7. PieChart
- **模板文件**: `pie-chart.ftl`
- **组件**: `GemPieChart`
- **数据类型**: `GemPieChartData`
- **参数**: `data`, `innerRadius`
- **用途**: 饼图，适合展示分类数据的比例关系

### 8. ScatterPlot
- **模板文件**: `scatter-plot.ftl`
- **组件**: `GemScatterPlot`
- **数据类型**: `GemScatterPlotData`
- **参数**: `data`, `xAxis`, `yAxis`
- **用途**: 散点图，适合展示两个变量之间的关系

### 9. SunburstChart
- **模板文件**: `sunburst-chart.ftl`
- **组件**: `GemSunburstChart`
- **数据类型**: `GemSunburstData`, `GemSDNshort`
- **参数**: `data`, `dataShort`, `colors`, `ignoreError`
- **用途**: 旭日图，适合展示层次化数据的分布

### 10. 特殊组件

#### DataTable
- **模板文件**: `enhanced-table-component.ftl`
- **用途**: 表格形式展示度量数据详情

#### TextDisplay
- **模板文件**: `text-display-component.ftl`
- **用途**: 文本形式展示不适合图表化的数据

## Chart-Widget Switch-Case 映射

在`chart-widget.ftl`中的switch-case分支：

```freemarker
<#switch chartType>
  <#case "PIE_CHART">
    ${tc.includeArgs("tpl.metrics.charts.pie-chart", [...])}
  <#case "BAR_CHART">
    ${tc.includeArgs("tpl.metrics.charts.bar-chart", [...])}
  <#case "LINE_CHART">
    ${tc.includeArgs("tpl.metrics.charts.line-chart", [...])}
  <#case "GAUGE_CHART">
    ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [...])}
  <#case "BULLET_CHART">
    ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [...])}
  <#case "CANDLESTICK_CHART">
    ${tc.includeArgs("tpl.metrics.charts.candlestick-chart", [...])}
  <#case "HEATMAP_CHART">
    ${tc.includeArgs("tpl.metrics.charts.heatmap-chart", [...])}
  <#case "SUNBURST_CHART">
    ${tc.includeArgs("tpl.metrics.charts.sunburst-chart", [...])}
  <#case "SCATTER_PLOT">
    ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [...])}
  <#case "DATA_TABLE">
    ${tc.includeArgs("tpl.metrics.charts.enhanced-table-component", [...])}
  <#default>
    ${tc.includeArgs("tpl.metrics.charts.text-display-component", [...])}
</#switch>
```

## 接口规范兼容性

所有图表类型都完全符合Chart Components.txt中的规范：
- ✅ 参数名称与规范一致
- ✅ 数据类型与规范匹配
- ✅ 组件调用格式正确
- ✅ 支持所有可选参数

这确保了cd2gui的度量可视化功能能够充分利用所有可用的图表组件。
