# 度量指标识别与计算接口 - 阶段2-3接口规范

*模块：度量指标识别与计算接口*  
*文档版本：v1.0*  
*接口：第二阶段（度量分析）→ 第三阶段（GUI可视化）*

## 接口概述

### 接口职责定义
**第二阶段（度量分析）**：负责从MontiCore AST中提取和分析度量指标，生成与现有cd2gui架构兼容的度量数据。
**第三阶段（GUI可视化）**：基于现有GuiModelFileCreator和FreeMarker模板系统，生成包含度量信息的GUI模型和可视化组件。

### 接口设计原则
1. **架构复用**：充分利用现有GuiModelFileCreator、CD2GUIAttribute等组件
2. **模板集成**：扩展现有FreeMarker模板系统支持度量可视化
3. **数据兼容**：度量数据格式与现有CD2GUIAttribute体系兼容
4. **向后兼容**：不影响现有GUI生成流程和数据结构
5. **渐进增强**：在现有overview、details、form页面基础上增加度量可视化能力

## 数据流接口设计

### 核心数据流
```
第二阶段 (度量分析)
├── AST结构分析
├── 度量指标计算
├── CD2GUIAttribute增强
└── 度量数据注入
    ↓
┌─────────────────────────────────────┐
│     GuiModelFileCreator增强          │
│  (基于现有架构的度量数据集成)          │
└─────────────────────────────────────┘
    ↓
第三阶段 (GUI可视化)
├── 扩展现有template (overview-gui.ftl等)
├── 新增度量可视化template
├── 生成包含度量信息的GUI模型
└── 渲染度量可视化组件
```

### 现有架构集成点
- **GuiModelFileCreator**：主要的GUI模型生成器，负责协调各种数据源
- **CD2GUIAttribute**：属性数据封装，需要扩展支持度量元数据
- **FreeMarker模板系统**：overview-gui.ftl、details-gui.ftl等模板需要扩展
- **AttributeManager**：属性管理器，需要支持度量相关属性处理
- **GeneratorEngine**：模板渲染引擎，复用现有生成流程

### 接口约定
- **数据格式**：扩展现有CD2GUIAttribute等数据结构，保持兼容性
- **传递方式**：通过GuiModelFileCreator的构造函数和方法参数传递
- **模板扩展**：新增度量相关FreeMarker模板，扩展现有模板
- **错误处理**：集成现有Log系统和异常处理机制

## 核心数据结构定义

### 1. CD2GUIAttribute增强（度量支持扩展）

```java
/**
 * 扩展现有CD2GUIAttribute以支持度量信息
 * 基于cd2gui/src/main/java/cd2gui/data/CD2GUIAttribute.java
 */
public class CD2GUIAttribute {
    // ...现有字段保持不变...
    
    // 新增度量相关字段
    private MetricProperties metricProperties;  // 度量属性
    private VisualizationHint visualizationHint; // 可视化建议
    private boolean hasMetrics;                 // 是否包含度量信息
    
    // 新增度量相关方法
    public boolean isMetricVisualizationRecommended() {
        return hasMetrics && visualizationHint != null 
               && visualizationHint.getRecommendedChart() != ChartType.NONE;
    }
    
    public ChartType getRecommendedChartType() {
        return visualizationHint != null ? 
               visualizationHint.getRecommendedChart() : ChartType.NONE;
    }
}
```

### 2. GuiModelFileCreator增强接口

```java
/**
 * 扩展GuiModelFileCreator以支持度量数据集成
 * 基于cd2gui/src/main/java/cd2gui/GuiModelFileCreator.java
 */
public class GuiModelFileCreator {
    // ...现有字段保持不变...
    
    // 新增度量相关字段
    private final Map<ASTCDClass, ClassMetrics> classMetricsMap = new HashMap<>();
    private final MetricTemplateManager metricTemplateManager;
    
    /**
     * 增强构造函数，支持度量数据注入
     */
    public GuiModelFileCreator(List<ASTCDClass> classes, 
                               List<CD2GUIClassTreeNode> classTrees, 
                               GeneratorSetup setup,
                               String domainName, 
                               String domainPackage,
                               Map<ASTCDClass, ClassMetrics> classMetrics) {
        // 调用现有构造函数
        this(classes, classTrees, setup, domainName, domainPackage);
        
        // 集成度量数据
        this.classMetricsMap.putAll(classMetrics);
        this.metricTemplateManager = new MetricTemplateManager(generator);
        
        // 增强现有属性映射
        enhanceAttributesWithMetrics();
    }
    
    /**
     * 增强现有概览页面生成，集成度量可视化
     */
    public void createMetricEnhancedOverviewPages() {
        for (ASTCDClass clazz : classes) {
            // 复用现有逻辑
            List<ASTCDClass> subclasses = ASTCDClassManager.getTreeNode(classTrees, clazz).getSubclasses();
            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);
            ClassMetrics metrics = classMetricsMap.get(clazz);
            
            // 生成增强的概览页面
            generator.generate("tpl/metric-enhanced-overview-gui.ftl",
                Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()), 
                       domainName.toLowerCase(), 
                       clazz.getName() + "MetricOverview.gui"),
                clazz, 
                attributes, 
                metrics, 
                subclasses);
        }
    }
}
```

### 3. 度量数据结构

```java
/**
 * 类级度量信息
 */
public class ClassMetrics {
    private String className;                   // 类名
    private List<AttributeMetric> attributeMetrics; // 属性度量列表
    private OverallStatistics overallStats;     // 整体统计
    private List<RecommendedVisualization> recommendations; // 可视化推荐
    
    // Getters for template usage
    public int getTotalAttributes() { 
        return attributeMetrics.size(); 
    }
    
    public int getVisualizableAttributesCount() {
        return (int) attributeMetrics.stream()
                    .filter(am -> am.getVisualizationHint()
                    .getRecommendedChart() != ChartType.NONE)
                    .count();
    }
    
    public List<AttributeMetric> getVisualizableAttributes() {
        return attributeMetrics.stream()
               .filter(am -> am.getVisualizationHint()
               .getRecommendedChart() != ChartType.NONE)
               .collect(Collectors.toList());
    }
}

/**
 * 属性级度量信息
 */
public class AttributeMetric {
    private String attributeName;               // 属性名
    private MetricScale scale;                  // 度量尺度
    private DataType dataType;                  // 数据类型
    private VisualizationHint visualizationHint; // 可视化建议
    private double confidence;                  // 分类置信度
    private Map<String, Object> metadata;       // 扩展元数据
}
```
    
### 4. 可视化建议与模板集成

```java
/**
 * 可视化建议信息
 */
public class VisualizationHint {
    private ChartType recommendedChart;         // 推荐图表类型
    private String templateName;                // 对应的FreeMarker模板名
    private Map<String, Object> templateData;   // 模板渲染数据
    private String description;                 // 可视化描述
    private double suitabilityScore;            // 适用性分数
    
    // 与现有模板系统集成
    public String getTemplateInclude() {
        return "tpl.metrics." + templateName;
    }
}

/**
 * 度量模板管理器
 */
public class MetricTemplateManager {
    private final GeneratorEngine generator;
    
    // 度量可视化模板映射
    private static final Map<ChartType, String> CHART_TEMPLATES = Map.of(
        ChartType.PIE_CHART, "pie-chart-widget",
        ChartType.BAR_CHART, "bar-chart-widget", 
        ChartType.LINE_CHART, "line-chart-widget",
        ChartType.SCATTER_PLOT, "scatter-plot-widget",
        ChartType.HISTOGRAM, "histogram-widget"
    );
    
    public void generateMetricVisualization(ASTCDClass clazz, 
                                          AttributeMetric metric,
                                          Path outputPath) {
        String templateName = CHART_TEMPLATES.get(metric.getVisualizationHint().getRecommendedChart());
        if (templateName != null) {
            generator.generate("tpl/metrics/" + templateName + ".ftl", 
                             outputPath, 
                             clazz, 
                             metric);
        }
    }
}
```

### 5. 枚举定义（与现有架构兼容）

```java
/**
 * 度量尺度枚举
 */
public enum MetricScale {
    NOMINAL("名义尺度", "分类数据，无顺序关系", "适合CD2GUIAttribute的枚举类型"),
    ORDINAL("序数尺度", "分类数据，有顺序关系", "适合有序枚举和等级数据"),
    INTERVAL("区间尺度", "数值数据，有顺序，无绝对零点", "适合CD2GUIAttribute的数值类型"),
    RATIO("比例尺度", "数值数据，有顺序，有绝对零点", "适合计数和测量类型");
    
    private final String displayName;
    private final String description;
    private final String cd2guiMapping;
}

/**
 * 图表类型枚举（对应GUI组件）
 */
public enum ChartType {
    PIE_CHART("饼图", "适合展示分类数据的比例关系", "pie-chart-component"),
    BAR_CHART("柱状图", "适合展示分类数据的数量对比", "bar-chart-component"), 
    LINE_CHART("折线图", "适合展示数值数据的趋势变化", "line-chart-component"),
    SCATTER_PLOT("散点图", "适合展示两个数值变量的关系", "scatter-plot-component"),
    HISTOGRAM("直方图", "适合展示数值数据的分布", "histogram-component"),
    DATA_TABLE("数据表格", "适合现有overview页面的表格展示", "enhanced-table-component"),
    NONE("不适合可视化", "数据不适合图表可视化", "text-display-component");
    
    private final String displayName;
    private final String description;
    private final String guiComponent;
}

/**
 * 数据类型枚举（对应CD2GUIAttribute类型系统）
 */
public enum DataType {
    NUMERIC("数值型", "对应CD2GUIAttribute.isNumeric()"),
    TEXT("文本型", "对应CD2GUIAttribute.isString()"),
    BOOLEAN("布尔型", "对应CD2GUIAttribute.isBoolean()"), 
    ENUM("枚举型", "对应CD2GUIAttribute.isEnum()"),
    DATE("日期型", "对应CD2GUIAttribute.isDate()"),
    COLLECTION("集合型", "对应CD2GUIAttribute.isList()"),
    REFERENCE("引用型", "对应CD2GUIAttribute.isClass()"),
    UNKNOWN("未知型", "无法分类的类型");
    
    private final String displayName;
    private final String cd2guiMapping;
}
```

## 模板系统集成规范

### 1. 现有模板扩展策略

```java
/**
 * 度量集成适配器 - 与现有GuiModelFileCreator无缝集成
 */
public class MetricIntegrationAdapter {
    
    /**
     * 增强现有overview页面模板数据
     */
    public Map<String, Object> enhanceOverviewTemplateData(ASTCDClass clazz,
                                                           List<CD2GUIAttribute> attributes,
                                                           ClassMetrics metrics) {
        Map<String, Object> templateData = new HashMap<>();
        
        // 现有数据保持不变
        templateData.put("domainClass", clazz);
        templateData.put("attributes", attributes);
        
        // 新增度量相关数据
        templateData.put("classMetrics", metrics);
        templateData.put("hasMetrics", metrics != null);
        templateData.put("visualizableAttributes", 
                        metrics != null ? metrics.getVisualizableAttributes() : Collections.emptyList());
        
        return templateData;
    }
    
    /**
     * 生成度量可视化片段
     */
    public void generateMetricWidgets(ASTCDClass clazz, 
                                    List<AttributeMetric> visualizableMetrics,
                                    Path outputDir) {
        for (AttributeMetric metric : visualizableMetrics) {
            String templateName = getTemplateForChartType(metric.getVisualizationHint().getRecommendedChart());
            generator.generate(templateName, 
                             outputDir.resolve("widgets/" + metric.getAttributeName() + "-widget.gui"),
                             clazz, 
                             metric);
        }
    }
}
```

### 2. FreeMarker模板扩展

```freemarker
<#-- 扩展的overview-gui.ftl模板 -->
<#-- 文件：tpl/metric-enhanced-overview-gui.ftl -->
${tc.signature("domainClass", "name", "domainPackage", "attributes", "classMetrics", "subclasses")}
/* (c) https://github.com/MontiCore/monticore */
package ${domainPackage?lower_case};

//data classes  
import ${domainPackage}.${name};

//GUI models
${tc.includeArgs("tpl.overview.imports-overview", [domainClass, name, attributes])}

<#-- 新增：度量可视化相关导入 -->
<#if classMetrics?? && classMetrics.hasVisualizableAttributes()>
${tc.includeArgs("tpl.metrics.imports-metrics", [domainClass, name, classMetrics.visualizableAttributes])}
</#if>

page ${name}Overview(List<${name}> ${name?lower_case}s) {

    <#-- 保持现有卡片概览 -->
    ${tc.includeArgs("tpl.overview.card-overview", [domainClass, name, attributes])}
    
    <#-- 新增：度量可视化面板 -->
    <#if classMetrics?? && classMetrics.hasVisualizableAttributes()>
    ${tc.includeArgs("tpl.metrics.visualization-panel", [domainClass, name, classMetrics])}
    </#if>
    
    <#-- 保持现有子类概览 -->
    <#if subclasses?size != 0 >
    ${tc.includeArgs("tpl.overview.subclass-overview", [domainClass, name, subclasses])}
    </#if>

    @GemNavItem(target = "/${domainPackage?lower_case}/${name}Form/", title = "Create new");
}
```

```freemarker
<#-- 新增度量可视化面板模板 -->
<#-- 文件：tpl/metrics/visualization-panel.ftl -->
${tc.signature("domainClass", "name", "classMetrics")}

<#-- 度量可视化面板 -->
component MetricsPanel {
    title "数据度量分析";
    
    <#-- 统计信息卡片 -->
    card StatisticsCard {
        title "类统计信息";
        content {
            text "总属性数: ${classMetrics.totalAttributes}";
            text "可视化属性数: ${classMetrics.visualizableAttributesCount}";
            <#if classMetrics.overallStats??>
            text "平均置信度: ${classMetrics.overallStats.averageConfidence?string("0.00")}";
            </#if>
        }
    }
    
    <#-- 可视化组件区域 -->
    <#list classMetrics.visualizableAttributes as metric>
    ${tc.includeArgs("tpl.metrics.chart-widget", [domainClass, name, metric])}
    </#list>
}
```

### 3. 图表组件模板

```freemarker
<#-- 图表组件模板 -->
<#-- 文件：tpl/metrics/chart-widget.ftl -->
${tc.signature("domainClass", "name", "metric")}

<#-- 根据图表类型选择对应组件 -->
<#switch metric.visualizationHint.recommendedChart.name()>
  <#case "PIE_CHART">
    ${tc.includeArgs("tpl.metrics.pie-chart-component", [domainClass, name, metric])}
    <#break>
  <#case "BAR_CHART">
    ${tc.includeArgs("tpl.metrics.bar-chart-component", [domainClass, name, metric])}
    <#break>
  <#case "DATA_TABLE">
    ${tc.includeArgs("tpl.metrics.enhanced-table-component", [domainClass, name, metric])}
    <#break>
  <#default>
    ${tc.includeArgs("tpl.metrics.text-display-component", [domainClass, name, metric])}
</#switch>
```
## 度量到GUI组件映射

### CD2GUIAttribute类型 → 度量尺度 → GUI组件映射

| CD2GUIAttribute类型 | 度量尺度 | 推荐GUI组件 | 现有模板复用 | 新增模板需求 |
|-------------------|---------|-------------|-------------|-------------|
| isEnum() | NOMINAL/ORDINAL | 下拉选择框 + 饼图 | form-gui.ftl | pie-chart-widget.ftl |
| isBoolean() | NOMINAL | 复选框 + 饼图 | form-gui.ftl | boolean-pie-chart.ftl |
| isString() | NOMINAL | 文本框 + 词云 | form-gui.ftl | word-cloud-widget.ftl |
| isNumeric() | INTERVAL/RATIO | 数值框 + 直方图 | form-gui.ftl | histogram-widget.ftl |
| isDate() | INTERVAL | 日期选择器 + 时间线 | form-gui.ftl | timeline-widget.ftl |
| isList() | 集合统计 | 表格 + 柱状图 | overview-gui.ftl | list-stats-widget.ftl |
| isClass() | 关联分析 | 详情页 + 关系图 | details-gui.ftl | relationship-graph.ftl |

### 可视化集成点

#### 1. Overview页面增强
- **现有功能**：基于`overview-gui.ftl`的表格展示
- **度量增强**：在表格上方添加度量可视化面板
- **集成方式**：扩展`createOverviewPages()`方法

#### 2. Details页面增强  
- **现有功能**：基于`details-gui.ftl`的详情展示
- **度量增强**：在详情页面添加属性度量分析
- **集成方式**：扩展`createDetailsPages()`方法

#### 3. Form页面增强
- **现有功能**：基于`form-gui.ftl`的表单输入
- **度量增强**：实时显示输入数据的度量特征
- **集成方式**：扩展`createFormPages()`方法

### 现有组件复用策略

```java
/**
 * GUI组件复用管理器
 */
public class ComponentReuseManager {
    
    /**
     * 基于现有CD2GUIAttribute判断可视化策略
     */
    public VisualizationStrategy determineStrategy(CD2GUIAttribute attribute, 
                                                  AttributeMetric metric) {
        VisualizationStrategy strategy = new VisualizationStrategy();
        
        // 复用现有组件判断逻辑
        if (attribute.isEnum()) {
            strategy.setBaseComponent("select-dropdown");
            strategy.setEnhancedComponent("enum-pie-chart");
            strategy.setTemplateBase("form-gui.ftl");
            strategy.setTemplateEnhancement("enum-visualization.ftl");
        } else if (attribute.isNumeric()) {
            strategy.setBaseComponent("number-input");
            strategy.setEnhancedComponent("numeric-histogram");
            strategy.setTemplateBase("form-gui.ftl");
            strategy.setTemplateEnhancement("numeric-visualization.ftl");
        }
        // ... 其他类型
        
        return strategy;
    }
}
```

## 错误处理与质量保证

### 1. 集成现有cd2gui错误处理机制

```java
/**
 * 度量数据验证器 - 集成现有Log系统
 */
public class MetricDataValidator {
    
    /**
     * 验证度量数据与现有CD2GUIAttribute的兼容性
     */
    public ValidationResult validateMetricCompatibility(ASTCDClass clazz, 
                                                       ClassMetrics metrics) {
        ValidationResult result = new ValidationResult();
        
        try {
            // 验证属性对应关系
            List<CD2GUIAttribute> attributes = AttributeManager.getAttributes(clazz);
            validateAttributeMapping(attributes, metrics.getAttributeMetrics(), result);
            
            // 验证可视化兼容性
            validateVisualizationCompatibility(attributes, metrics, result);
            
        } catch (Exception e) {
            Log.error("度量数据验证失败: " + e.getMessage(), "MetricDataValidator");
            result.addError("验证过程异常: " + e.getMessage());
        }
        
        return result;
    }
    
    private void validateAttributeMapping(List<CD2GUIAttribute> attributes,
                                        List<AttributeMetric> metrics,
                                        ValidationResult result) {
        // 确保每个CD2GUIAttribute都有对应的度量信息
        for (CD2GUIAttribute attr : attributes) {
            if (attr.isVisibleInOverview()) {
                boolean hasMetric = metrics.stream()
                    .anyMatch(m -> m.getAttributeName().equals(attr.getName()));
                
                if (!hasMetric) {
                    Log.warn("属性 " + attr.getName() + " 缺少度量信息", "MetricDataValidator");
                    result.addWarning("属性 " + attr.getName() + " 缺少度量信息");
                }
            }
        }
    }
    
    private void validateVisualizationCompatibility(List<CD2GUIAttribute> attributes,
                                                  ClassMetrics metrics,
                                                  ValidationResult result) {
        for (AttributeMetric metric : metrics.getAttributeMetrics()) {
            CD2GUIAttribute attr = findAttributeByName(attributes, metric.getAttributeName());
            if (attr != null) {
                validateChartTypeCompatibility(attr, metric, result);
            }
        }
    }
    
    private void validateChartTypeCompatibility(CD2GUIAttribute attr,
                                              AttributeMetric metric,
                                              ValidationResult result) {
        ChartType recommendedChart = metric.getVisualizationHint().getRecommendedChart();
        
        // 基于CD2GUIAttribute类型验证图表兼容性
        if (attr.isEnum() && recommendedChart == ChartType.LINE_CHART) {
            result.addError("枚举类型不适合使用折线图: " + attr.getName());
        } else if (attr.isString() && recommendedChart == ChartType.HISTOGRAM) {
            result.addError("字符串类型不适合使用直方图: " + attr.getName());
        }
        // ... 其他兼容性检查
    }
}
```

### 2. 错误恢复与降级策略

```java
/**
 * 度量可视化降级策略
 */
public class VisualizationFallbackStrategy {
    
    /**
     * 当度量数据不可用时的降级处理
     */
    public void handleMetricDataUnavailable(ASTCDClass clazz, 
                                          GuiModelFileCreator creator) {
        Log.warn("类 " + clazz.getName() + " 的度量数据不可用，使用标准GUI生成", 
                "VisualizationFallback");
        
        // 降级到现有标准流程
        creator.createOverviewPages();  // 使用现有方法
        creator.createDetailsPages();
        creator.createFormPages();
    }
    
    /**
     * 当特定图表类型不支持时的降级处理
     */
    public ChartType getFallbackChartType(CD2GUIAttribute attribute, 
                                        ChartType requestedType) {
        if (attribute.isEnum()) {
            return ChartType.PIE_CHART;  // 枚举类型默认使用饼图
        } else if (attribute.isNumeric()) {
            return ChartType.BAR_CHART;  // 数值类型默认使用柱状图
        } else {
            return ChartType.DATA_TABLE; // 其他类型使用表格
        }
    }
}
```

### 3. 性能监控与优化

```java
/**
 * 度量可视化性能监控
 */
public class MetricVisualizationPerformanceMonitor {
    
    private static final long MAX_PROCESSING_TIME_MS = 5000; // 合理超时时间
    
    /**
     * 监控度量数据处理性能
     */
    public ProcessingResult monitorMetricProcessing(ASTCDClass clazz,
                                                   Supplier<ClassMetrics> processor) {
        long startTime = System.currentTimeMillis();
        
        try {
            ClassMetrics result = processor.get();
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (processingTime > MAX_PROCESSING_TIME_MS) {
                Log.warn(String.format("类 %s 的度量处理耗时过长: %dms", 
                        clazz.getName(), processingTime), 
                        "MetricPerformanceMonitor");
            }
            
            return ProcessingResult.success(result, processingTime);
            
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            Log.error(String.format("类 %s 的度量处理失败，耗时: %dms, 错误: %s", 
                     clazz.getName(), processingTime, e.getMessage()), 
                     "MetricPerformanceMonitor");
            
            return ProcessingResult.failure(e, processingTime);
        }
    }
}
```


## 集成测试与验证

### 1. 基于现有cd2gui测试框架的集成测试

```java
/**
 * 度量可视化集成测试
 * 基于现有cd2gui测试体系
 */
@ExtendWith(MockitoExtension.class)
public class MetricVisualizationIntegrationTest {
    
    @Mock
    private GeneratorEngine mockGenerator;
    
    @Test
    public void testMetricEnhancedOverviewGeneration() {
        // 准备测试数据 - 使用现有cd2gui测试数据
        ASTCDClass testClass = CD2GUITestUtils.createSampleClass("TestClass");
        List<CD2GUIAttribute> attributes = AttributeManager.getAttributes(testClass);
        
        // 创建度量数据
        ClassMetrics metrics = createSampleMetrics(testClass);
        
        // 测试增强的GuiModelFileCreator
        GuiModelFileCreator creator = new GuiModelFileCreator(
            List.of(testClass), 
            createClassTrees(testClass),
            createGeneratorSetup(),
            "TestDomain",
            "com.test",
            Map.of(testClass, metrics)
        );
        
        // 执行生成
        assertDoesNotThrow(() -> creator.createMetricEnhancedOverviewPages());
        
        // 验证模板调用
        verify(mockGenerator).generate(
            eq("tpl/metric-enhanced-overview-gui.ftl"), 
            any(Path.class),
            eq(testClass),
            eq(attributes),
            eq(metrics),
            any()
        );
    }
    
    @Test
    public void testFallbackToStandardGeneration() {
        // 测试度量数据不可用时的降级处理
        ASTCDClass testClass = CD2GUITestUtils.createSampleClass("TestClass");
        
        GuiModelFileCreator creator = new GuiModelFileCreator(
            List.of(testClass),
            createClassTrees(testClass),
            createGeneratorSetup(),
            "TestDomain", 
            "com.test"
            // 注意：不提供度量数据
        );
        
        // 应该降级到标准生成流程
        assertDoesNotThrow(() -> creator.createOverviewPages());
        
        // 验证使用了标准模板
        verify(mockGenerator).generate(
            eq("tpl/overview-gui.ftl"),
            any(Path.class),
            eq(testClass),
            any(),
            any()
        );
    }
    
    @Test
    public void testTemplateDataCompatibility() {
        // 测试模板数据与现有模板的兼容性
        ASTCDClass testClass = CD2GUITestUtils.createSampleClass("TestClass");
        ClassMetrics metrics = createSampleMetrics(testClass);
        
        MetricIntegrationAdapter adapter = new MetricIntegrationAdapter();
        Map<String, Object> templateData = adapter.enhanceOverviewTemplateData(
            testClass, 
            AttributeManager.getAttributes(testClass),
            metrics
        );
        
        // 验证现有模板所需数据仍然存在
        assertTrue(templateData.containsKey("domainClass"));
        assertTrue(templateData.containsKey("attributes"));
        
        // 验证新增度量数据
        assertTrue(templateData.containsKey("classMetrics"));
        assertTrue(templateData.containsKey("hasMetrics"));
    }
}
```

### 2. 性能测试

```java
/**
 * 度量可视化性能测试
 */
public class MetricVisualizationPerformanceTest {
    
    @Test
    public void testLargeClassProcessing() {
        // 测试大型类的度量处理性能
        ASTCDClass largeClass = createLargeClass(100); // 100个属性
        ClassMetrics metrics = createLargeClassMetrics(largeClass);
        
        long startTime = System.currentTimeMillis();
        
        GuiModelFileCreator creator = new GuiModelFileCreator(
            List.of(largeClass),
            createClassTrees(largeClass),
            createGeneratorSetup(),
            "TestDomain",
            "com.test",
            Map.of(largeClass, metrics)
        );
        
        creator.createMetricEnhancedOverviewPages();
        
        long endTime = System.currentTimeMillis();
        long processingTime = endTime - startTime;
        
        // 验证处理时间在可接受范围内
        assertTrue(processingTime < 10000, // 合理范围内完成
                  "大型类处理时间过长: " + processingTime + "ms");
    }
    
    @Test
    public void testConcurrentClassProcessing() {
        // 测试并发处理多个类
        List<ASTCDClass> classes = createMultipleClasses(20);
        Map<ASTCDClass, ClassMetrics> metricsMap = createMetricsForClasses(classes);
        
        ExecutorService executor = Executors.newFixedThreadPool(5);
        List<Future<Void>> futures = new ArrayList<>();
        
        for (ASTCDClass clazz : classes) {
            futures.add(executor.submit(() -> {
                GuiModelFileCreator creator = new GuiModelFileCreator(
                    List.of(clazz),
                    createClassTrees(clazz),
                    createGeneratorSetup(),
                    "TestDomain",
                    "com.test",
                    Map.of(clazz, metricsMap.get(clazz))
                );
                
                creator.createMetricEnhancedOverviewPages();
                return null;
            }));
        }
        
        // 验证所有任务都成功完成
        assertDoesNotThrow(() -> {
            for (Future<Void> future : futures) {
                future.get(30, TimeUnit.SECONDS);
            }
        });
    }
}
```


## 实施协调清单

### 第二阶段（度量分析）开发者需要实现：

1. **度量数据生成**
   - [ ] 实现基于现有CD2GUIAttribute的度量分析算法
   - [ ] 生成与现有数据结构兼容的ClassMetrics对象
   - [ ] 确保度量数据与AttributeManager管理的属性一一对应
   - [ ] 实现置信度计算和质量评估

2. **现有组件集成**
   - [ ] 扩展现有CD2GUIAttribute类，添加度量相关字段和方法
   - [ ] 实现MetricIntegrationAdapter，确保与现有GuiModelFileCreator无缝集成
   - [ ] 提供度量数据的getter方法，支持FreeMarker模板访问
   - [ ] 集成现有Log系统，统一错误处理和日志记录

3. **质量保证**
   - [ ] 实现基于现有验证机制的度量数据验证
   - [ ] 提供降级策略，确保度量数据不可用时不影响标准GUI生成
   - [ ] 实现性能监控，确保度量处理在合理时间范围内
   - [ ] 提供详细的分析追踪信息和错误诊断

### 第三阶段（GUI可视化）开发者需要实现：

1. **模板系统扩展**
   - [ ] 创建degree量可视化相关的FreeMarker模板（tpl/metrics/目录）
   - [ ] 扩展现有overview-gui.ftl、details-gui.ftl等模板，支持度量数据
   - [ ] 实现图表组件模板（pie-chart-widget.ftl、bar-chart-widget.ftl等）
   - [ ] 确保新模板与现有模板导入和包含机制兼容

2. **GUI组件开发**
   - [ ] 基于现有GUI组件体系开发度量可视化组件
   - [ ] 实现数据表格增强组件，在现有表格基础上添加度量信息
   - [ ] 开发图表组件，支持饼图、柱状图、直方图等常用图表类型
   - [ ] 实现可视化面板组件，整合多个图表和统计信息

3. **GuiModelFileCreator增强**
   - [ ] 扩展现有GuiModelFileCreator构造函数，支持度量数据注入
   - [ ] 实现createMetricEnhancedOverviewPages()等增强方法
   - [ ] 保持与现有创建流程的兼容性，确保不影响标准GUI生成
   - [ ] 实现MetricTemplateManager，管理度量相关模板的生成和渲染

### 协调任务：

1. **接口规范确认**
   - [ ] 确认CD2GUIAttribute扩展字段和方法签名
   - [ ] 确认ClassMetrics和AttributeMetric数据结构
   - [ ] 确认GuiModelFileCreator增强构造函数参数
   - [ ] 确认FreeMarker模板数据传递格式

2. **现有架构集成验证**
   - [ ] 验证度量数据与现有AttributeManager的兼容性
   - [ ] 验证扩展模板与现有模板引擎的集成
   - [ ] 验证增强的GuiModelFileCreator与现有生成流程的兼容性
   - [ ] 验证错误处理与现有Log系统的集成

3. **测试协调**
   - [ ] 基于现有cd2gui测试框架制定联合测试计划
   - [ ] 使用现有测试数据和工具类进行集成测试
   - [ ] 实施降级和错误恢复场景测试
   - [ ] 验证性能要求和并发处理能力

4. **文档和部署**
   - [ ] 更新现有cd2gui使用文档，说明度量可视化功能
   - [ ] 提供度量模板开发指南
   - [ ] 编写故障排除和性能优化指南
   - [ ] 维护向后兼容性和版本升级路径

### 实施优先级：

**高优先级（必须实现）**：
- CD2GUIAttribute扩展和GuiModelFileCreator增强
- 基础模板扩展和降级策略
- 与现有架构的兼容性验证

**中优先级（重要功能）**：
- 图表组件开发和可视化面板
- 性能优化和错误处理增强
- 详细的测试覆盖

**低优先级（增强功能）**：
- 高级图表类型支持
- 交互式可视化功能
- 自定义可视化配置

---

*本接口规范文档基于现有cd2gui架构设计，确保度量分析和GUI可视化功能与现有系统的无缝集成，保持向后兼容性的同时提供强大的度量可视化能力。*
