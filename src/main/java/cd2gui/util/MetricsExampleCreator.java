/* (c) https://github.com/MontiCore/monticore */
package cd2gui.util;

import cd2gui.data.CD2GUIMetric;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Example class showing how to create multiple different types of metrics for a single class.
 * This demonstrates that metrics are not mutually exclusive - a class can have multiple
 * different chart types and simple values.
 */
public class MetricsExampleCreator {
    
    /**
     * Creates example metrics for a Car class showing different visualization types.
     * This shows how one class can have multiple different types of metrics.
     */
    public static List<CD2GUIMetric> createCarMetrics() {
        List<CD2GUIMetric> metrics = new ArrayList<>();
        
        // 1. Simple value metric - Total count
        CD2GUIMetric totalCars = CD2GUIMetric.createSimpleValueMetric(
            "totalCars", 
            "Total Cars", 
            "getTotalCarCount()"
        );
        totalCars.setUnit("cars");
        totalCars.setDescription("Total number of cars in the system");
        totalCars.setColor("#2563eb");
        metrics.add(totalCars);
        
        // 2. Pie chart metric - Status distribution
        CD2GUIMetric statusDistribution = CD2GUIMetric.createPieMetric(
            "statusDistribution",
            "Car Status Distribution", 
            "getCarStatusDistribution()"
        );
        statusDistribution.setDescription("Distribution of car availability status");
        statusDistribution.setInnerRadius(40);
        metrics.add(statusDistribution);
        
        // 3. Bar chart metric - Cars by manufacturer
        CD2GUIMetric manufacturerCount = CD2GUIMetric.createBarMetric(
            "manufacturerCount",
            "Cars by Manufacturer",
            "getCarsByManufacturer()"
        );
        manufacturerCount.setDescription("Number of cars grouped by manufacturer");
        manufacturerCount.setStacked(false);
        metrics.add(manufacturerCount);
        
        // 4. Line chart metric - Rental trends over time
        CD2GUIMetric rentalTrends = CD2GUIMetric.createLineMetric(
            "rentalTrends",
            "Rental Trends",
            "getRentalTrendsData()"
        );
        rentalTrends.setDescription("Car rental trends over the past 12 months");
        rentalTrends.setBackgroundColorEnabled(true);
        metrics.add(rentalTrends);
        
        // 5. Gauge metric - Average utilization
        CD2GUIMetric utilization = CD2GUIMetric.createGaugeMetric(
            "utilization",
            "Fleet Utilization",
            "getUtilizationData()"
        );
        utilization.setMinValue(0);
        utilization.setMaxValue(100);
        utilization.setUnit("%");
        utilization.setTextValue("Fleet Utilization Rate");
        utilization.setDescription("Current fleet utilization percentage");
        metrics.add(utilization);
        
        // 6. Bullet chart metric - Performance vs target
        CD2GUIMetric performance = CD2GUIMetric.createBulletMetric(
            "performance",
            "Monthly Performance",
            85, // current value
            90, // reference/target
            95  // threshold
        );
        performance.setTitle("Monthly Performance");
        performance.setSubtitle("vs Target");
        performance.setRange(Arrays.asList(50, 75, 100));
        performance.setSteps(Arrays.asList(25, 50, 75, 100));
        performance.setDescription("Current month performance against targets");
        metrics.add(performance);
        
        return metrics;
    }
    
    /**
     * Creates example metrics for a Student class showing different combinations.
     */
    public static List<CD2GUIMetric> createStudentMetrics() {
        List<CD2GUIMetric> metrics = new ArrayList<>();
        
        // Simple value - enrollment count
        CD2GUIMetric enrollment = CD2GUIMetric.createSimpleValueMetric(
            "enrollment", 
            "Total Enrollment", 
            "getStudentCount()"
        );
        enrollment.setUnit("students");
        enrollment.setTrend("up");
        metrics.add(enrollment);
        
        // Pie chart - grade distribution
        CD2GUIMetric gradeDistribution = CD2GUIMetric.createPieMetric(
            "gradeDistribution",
            "Grade Distribution",
            "getGradeDistribution()"
        );
        gradeDistribution.setDescription("Distribution of student grades");
        metrics.add(gradeDistribution);
        
        // Bar chart - students by major
        CD2GUIMetric majorDistribution = CD2GUIMetric.createBarMetric(
            "majorDistribution",
            "Students by Major",
            "getStudentsByMajor()"
        );
        majorDistribution.setStacked(true);
        metrics.add(majorDistribution);
        
        return metrics;
    }
}
