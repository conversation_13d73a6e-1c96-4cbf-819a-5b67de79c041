/* (c) https://github.com/MontiCore/monticore */
package cd2gui.data;

import java.util.List;

/**
 * Represents a metric that can be displayed in the metrics GUI page.
 * This class encapsulates different types of metrics including simple values,
 * percentages, and chart-based visualizations following Chart Components.txt specification.
 */
public class CD2GUIMetric {
    
    private String name;
    private String displayName;
    private String description;
    private String unit;
    private String color;
    private MetricType type;
    private Object value;
    private String valueExpression;
    private Object chartData;
    private String chartType;
    private Integer innerRadius;
    
    // Chart-specific properties
    private Boolean stacked;
    private Boolean backgroundColorEnabled;
    private Integer maxValue;
    private Integer minValue;
    private String textValue;
    private String label;
    private String trend;
    
    // Bullet chart specific properties
    private Integer reference;
    private Integer thresholdValue;
    private String title;
    private String subtitle;
    private List<Integer> range;
    private List<Integer> steps;
    
    public enum MetricType {
        NUMERIC,
        PERCENTAGE, 
        CHART,
        TEXT
    }
    
    public CD2GUIMetric(String name, String displayName, MetricType type) {
        this.name = name;
        this.displayName = displayName;
        this.type = type;
    }
    
    // Basic getters and setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDisplayName() { return displayName != null ? displayName : name; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    
    public String getColor() { return color; }
    public void setColor(String color) { this.color = color; }
    
    public MetricType getType() { return type; }
    public void setType(MetricType type) { this.type = type; }
    
    public Object getValue() { return value; }
    public void setValue(Object value) { this.value = value; }
    
    public String getValueExpression() { return valueExpression; }
    public void setValueExpression(String valueExpression) { this.valueExpression = valueExpression; }
    
    public Object getChartData() { return chartData; }
    public void setChartData(Object chartData) { this.chartData = chartData; }
    
    public String getChartType() { return chartType; }
    public void setChartType(String chartType) { this.chartType = chartType; }
    
    public Integer getInnerRadius() { return innerRadius; }
    public void setInnerRadius(Integer innerRadius) { this.innerRadius = innerRadius; }
    
    // Chart-specific getters and setters
    public Boolean isStacked() { return stacked; }
    public void setStacked(Boolean stacked) { this.stacked = stacked; }
    
    public Boolean isBackgroundColorEnabled() { return backgroundColorEnabled; }
    public void setBackgroundColorEnabled(Boolean backgroundColorEnabled) { this.backgroundColorEnabled = backgroundColorEnabled; }
    
    public Integer getMaxValue() { return maxValue; }
    public void setMaxValue(Integer maxValue) { this.maxValue = maxValue; }
    
    public Integer getMinValue() { return minValue; }
    public void setMinValue(Integer minValue) { this.minValue = minValue; }
    
    public String getTextValue() { return textValue; }
    public void setTextValue(String textValue) { this.textValue = textValue; }
    
    public String getLabel() { return label; }
    public void setLabel(String label) { this.label = label; }
    
    public String getTrend() { return trend; }
    public void setTrend(String trend) { this.trend = trend; }
    
    // Bullet chart getters and setters
    public Integer getReference() { return reference; }
    public void setReference(Integer reference) { this.reference = reference; }
    
    public Integer getThresholdValue() { return thresholdValue; }
    public void setThresholdValue(Integer thresholdValue) { this.thresholdValue = thresholdValue; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getSubtitle() { return subtitle; }
    public void setSubtitle(String subtitle) { this.subtitle = subtitle; }
    
    public List<Integer> getRange() { return range; }
    public void setRange(List<Integer> range) { this.range = range; }
    
    public List<Integer> getSteps() { return steps; }
    public void setSteps(List<Integer> steps) { this.steps = steps; }
    
    public boolean hasChartData() { return chartData != null; }
}
