${tc.signature(title, data, innerRadius, width, height, titleComponent)}

<#--
metric-card.ftl
A reusable GemCard wrapper that embeds a pie‑chart (or any other component you may swap in).

Parameters
    - title          : String                                     → Card title (ignored if titleComponent is given)
    - data           : GemPieChartTypes.GemPieChartData            → Data object for the pie chart
    - innerRadius    : Number (optional, default = 50)             → Doughnut hole size
    - width          : String (optional, e.g. "50%")               → CSS‑like width for the card
    - height         : String (optional, e.g. "300px")             → CSS‑like height for the card
    - titleComponent : GemComponent (optional)                     → Custom component shown in title bar (e.g. a GemLink)

  Usage example in a page template:
    ${tc.includeArgs("tpl.charts.metric-card", ["Issue distribution", pieData, 40, "50%", "300px", @GemLink(url="/docs", text="Docs")])}
-->

<#-- Fallbacks for optional arguments -->
<#assign _inner = innerRadius?default(50)>

@GemCard(
<#if width??>width = "${width}",</#if>
<#if height??>height = "${height}",</#if>
<#if titleComponent??>
titleComponent = ${titleComponent},
  <#else>
    title = "${title}",
  </#if>
  component = ${tc.includeArgs("tpl.charts.pie-chart", [data, _inner])}
);