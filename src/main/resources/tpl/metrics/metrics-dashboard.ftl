<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metrics")}

${name?uncap_first}MetricsDashboard@GemCard(
  title = "${name} Metrics Dashboard",
  component = ${name?uncap_first}MetricsDashboardColumn@GemColumn(rowGap = "10px", components = [

    <#-- Navigation back to overview and details -->
    ${name?uncap_first}MetricsNavRow@GemRow(hAlign = "space-between", components = [
      <#if !attrManager.isOption("NoOverview")>
        ${name?uncap_first}Metrics_ToOverview@GemNavItem(target = "/${linkPath}/${name}Overview", title = "To Overview"),
      </#if>
      <#if !attrManager.isOption("NoDetails")>
        ${name?uncap_first}Metrics_ToDetails@GemNavItem(target = "/${linkPath}/${name}Details/" + ${name?lower_case}.gemId, title = "To Details"),
      </#if>
      <#if !attrManager.isOption("NoDashboard")>
        ${name?uncap_first}Metrics_ToDashboard@GemNavItem(target = "/${linkPath}/CD2GUIDashboard", title = "To Dashboard")
      </#if>
    ]),

    <#-- Metrics Display -->
    <#if metrics?has_content>
      ${name?uncap_first}MetricsGrid@GemRow(wrap = "wrap", rowGap = "10px", hAlign = "space-evenly", components = [
        <#list metrics as metric>
          ${tc.includeArgs("metric-display", [domainClass, name, metric])}<#sep>,</#sep>
        </#list>
      ])
    <#else>
      ${name?uncap_first}NoMetrics@GemText(value = "No metrics available for this ${name}")
    </#if>
  ])
);
