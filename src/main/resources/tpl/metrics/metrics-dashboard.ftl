<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "classMetrics")}

<#-- Metrics visualization panel integrated into overview page -->
${name?uncap_first}MetricsPanel@GemCard(
  title = "${name} Data Metrics Analysis",
  component = ${name?uncap_first}MetricsPanelColumn@GemColumn(rowGap = "15px", components = [

    <#-- Statistics summary -->
    <#if classMetrics?? && classMetrics.overallStats??>
    ${name?uncap_first}MetricsStatsRow@GemRow(hAlign = "space-evenly", components = [
      ${name?uncap_first}TotalAttrs@GemText(value = "Total Attributes: ${classMetrics.totalAttributes}"),
      ${name?uncap_first}VisualizableAttrs@GemText(value = "Visualizable: ${classMetrics.visualizableAttributesCount}"),
      <#if classMetrics.overallStats.averageConfidence??>
      ${name?uncap_first}AvgConfidence@GemText(value = "Avg Confidence: ${classMetrics.overallStats.averageConfidence?string('0.00')}")
      </#if>
    ]),
    </#if>

    <#-- Metrics visualization grid -->
    <#if classMetrics?? && classMetrics.visualizableAttributes?has_content>
      ${name?uncap_first}MetricsGrid@GemRow(wrap = "wrap", rowGap = "15px", columnGap = "15px", hAlign = "space-evenly", components = [
        <#list classMetrics.visualizableAttributes as metric>
          <#-- Each metric is rendered with its recommended chart type -->
          ${tc.includeArgs("metric-display", [domainClass, name, metric])}<#sep>,</#sep>
        </#list>
      ])
    <#else>
      ${name?uncap_first}NoMetrics@GemText(
        value = "No visualizable metrics available for ${name}",
        fontSize = "14px",
        color = "#6b7280",
        textAlign = "center"
      )
    </#if>
  ])
);
