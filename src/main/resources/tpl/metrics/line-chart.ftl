${tc.signature(data, width, height)}

<#--
line-chart.ftl for metrics
Wrapper around GemLine<PERSON>hart specifically for metrics display.

Parameters
- data   : GemLineChartTypes.GemLineChartData  → Prepared data object containing the lines.
- width  : String (optional)                   → Chart width (e.g., "100%")
- height : String (optional)                   → Chart height (e.g., "200px")

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.line-chart", [data, "100%", "180px"])}
-->

@GemLineChart(
    data = ${data}
    <#if width??>,width = "${width}"</#if>
    <#if height??>,height = "${height}"</#if>
);
