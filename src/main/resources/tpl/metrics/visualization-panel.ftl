 <#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "classMetrics")}

<#-- Metrics visualization panel for integration into overview pages -->
${name?uncap_first}VisualizationPanel@GemCard(
  title = "Data Metrics Visualization",
  component = ${name?uncap_first}VisualizationPanelColumn@GemColumn(rowGap = "15px", components = [

    <#-- Statistics summary -->
    <#if classMetrics?? && classMetrics.overallStats??>
    ${name?uncap_first}MetricsStatsCard@GemCard(
      title = "Statistics Summary",
      component = ${name?uncap_first}StatsRow@GemRow(hAlign = "space-evenly", components = [
        ${name?uncap_first}TotalAttrsText@GemText(value = "Total Attributes: ${classMetrics.totalAttributes}"),
        ${name?uncap_first}VisualizableAttrsText@GemText(value = "Visualizable: ${classMetrics.visualizableAttributesCount}"),
        <#if classMetrics.overallStats.averageConfidence??>
        ${name?uncap_first}AvgConfidenceText@GemText(value = "Avg Confidence: ${classMetrics.overallStats.averageConfidence?string('0.00')}")
        </#if>
      ])
    ),
    </#if>

    <#-- Metrics visualization grid - direct display without dashboard wrapper -->
    <#if classMetrics?? && classMetrics.hasVisualizableAttributes()>
      ${name?uncap_first}VisualizationGrid@GemRow(
        wrap = "wrap",
        hAlign = "space-evenly",
        components = [
          <#list classMetrics.visualizableAttributes as attributeMetric>
            ${tc.includeArgs("tpl.metrics.chart-widget", [domainClass, name, attributeMetric])}<#sep>,</#sep>
          </#list>
        ]
      )
    <#else>
      <#-- No visualizable metrics message -->
      ${name?uncap_first}NoVisualizableMetrics@GemCard(
        title = "No Visualizable Metrics",
        component = ${name?uncap_first}NoMetricsColumn@GemColumn(
          hAlign = "center",
          vAlign = "center",
          components = [
            ${name?uncap_first}NoMetricsText@GemText(
              value = "No visualizable metrics are available for ${name} attributes.",
            ),
            ${name?uncap_first}NoMetricsHint@GemText(
              value = "Metrics analysis may not have been performed or attributes may not be suitable for visualization.",
            )
          ]
        )
      )
    </#if>
  ])
);
