${tc.signature(title, content, width, height, description)}

<#--
metric-card.ftl
A reusable GemCard wrapper for metrics that can contain any type of content.

Parameters
- title       : String                    → Card title
- content     : String                    → The content to display (chart, text, etc.)
- width       : String (optional)         → CSS-like width for the card (default: "40%")
- height      : String (optional)         → CSS-like height for the card (default: "200px")
- description : String (optional)         → Optional description text

Usage example in metrics templates:
  ${tc.includeArgs("tpl.metrics.metric-card", ["Sales Count", chartContent, "45%", "250px", "Monthly sales data"])}
-->

<#-- Fallbacks for optional arguments -->
<#assign _width = width?default("40%")>
<#assign _height = height?default("200px")>

@GemCard(
  width = "${_width}",
  height = "${_height}",
  title = "${title}",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    rowGap = "5px",
    components = [
      ${content}
      <#if description??>,
      @GemText(
        value = "${description}",
        fontSize = "12px",
        color = "#9ca3af",
        textAlign = "center"
      )
      </#if>
    ]
  )
);
