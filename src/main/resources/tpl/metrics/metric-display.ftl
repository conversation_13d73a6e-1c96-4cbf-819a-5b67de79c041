<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#-- Generate content based on visualization hint - following interface specification -->
<#assign metricContent>
  <#-- Get the recommended chart type from visualization hint -->
  <#if attributeMetric.visualizationHint?? && attributeMetric.visualizationHint.recommendedChart??>
    <#assign chartType = attributeMetric.visualizationHint.recommendedChart.name()>

    <#-- Render based on recommended chart type -->
    <#switch chartType>
      <#case "PIE_CHART">
        ${tc.includeArgs("tpl.metrics.charts.pie-chart", [attributeMetric.visualizationHint.templateData.data!{}, attributeMetric.visualizationHint.templateData.innerRadius!50])}
        <#break>
      <#case "BAR_CHART">
        ${tc.includeArgs("tpl.metrics.charts.bar-chart", [attributeMetric.visualizationHint.templateData.data!{}, attributeMetric.visualizationHint.templateData.width!"100%", attributeMetric.visualizationHint.templateData.height!"200px"])}
        <#break>
      <#case "LINE_CHART">
        ${tc.includeArgs("tpl.metrics.charts.line-chart", [attributeMetric.visualizationHint.templateData.data!{}, attributeMetric.visualizationHint.templateData.width!"100%", attributeMetric.visualizationHint.templateData.height!"200px"])}
        <#break>
      <#case "GAUGE_CHART">
        ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [attributeMetric.visualizationHint.templateData.data!{}, attributeMetric.visualizationHint.templateData.min!0, attributeMetric.visualizationHint.templateData.max!100, attributeMetric.visualizationHint.templateData.unit!"", attributeMetric.visualizationHint.templateData.textValue!""])}
        <#break>
      <#case "BULLET_CHART">
        ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [attributeMetric.visualizationHint.templateData.value!0, attributeMetric.visualizationHint.templateData.reference!0, attributeMetric.visualizationHint.templateData.thresholdValue!0, attributeMetric.visualizationHint.templateData.title!"", attributeMetric.visualizationHint.templateData.subtitle!"", attributeMetric.visualizationHint.templateData.range![], attributeMetric.visualizationHint.templateData.steps![]])}
        <#break>
      <#default>
        <#-- Default to simple value display -->
        ${tc.includeArgs("tpl.metrics.charts.simple-value", [attributeMetric.metadata.value!"N/A", attributeMetric.metadata.unit!"", attributeMetric.attributeName, attributeMetric.metadata.color!"#2563eb", attributeMetric.metadata.trend!""])}
    </#switch>
  <#else>
    <#-- No visualization hint - use simple value display -->
    ${tc.includeArgs("tpl.metrics.charts.simple-value", [attributeMetric.metadata.value!"N/A", attributeMetric.metadata.unit!"", attributeMetric.attributeName, attributeMetric.metadata.color!"#6b7280", ""])}
  </#if>
</#assign>

<#-- Render the metric using the metric-wrapper template -->
${tc.includeArgs("tpl.metrics.metric-wrapper", [
  attributeMetric.attributeName + " Analysis",
  metricContent,
  "45%",
  "220px",
  attributeMetric.visualizationHint.description!"Metric analysis for " + attributeMetric.attributeName
])}
