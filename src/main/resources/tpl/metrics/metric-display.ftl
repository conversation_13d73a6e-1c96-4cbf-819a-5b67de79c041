<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metric")}

<#--
  Renders a single metric display as a card
  Flexible template that adapts to different metric properties
-->

<#-- Prepare the content based on metric type -->
<#if metric.hasChartData()!false>
  <#-- Chart-based metric -->
  <#if metric.getChartType()! == "PIE">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.pie-chart", [metric.getChartData(), metric.getInnerRadius()!50])}</#assign>
  <#elseif metric.getChartType()! == "BAR">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.bar-chart", [metric.getChartData(), metric.isStacked()!false, metric.getMaxValue()!"", metric.getMinValue()!""])}</#assign>
  <#elseif metric.getChartType()! == "LINE">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.line-chart", [metric.getChartData(), metric.isBackgroundColorEnabled()!false, metric.getMaxValue()!"", metric.getMinValue()!""])}</#assign>
  <#elseif metric.getChartType()! == "GAUGE">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.gauge-chart", [metric.getChartData(), metric.getMinValue()!"", metric.getMaxValue()!"", metric.getUnit()!"", metric.getTextValue()!""])}</#assign>
  <#elseif metric.getChartType()! == "BULLET">
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.bullet-chart", [metric.getValue()!0, metric.getReference()!0, metric.getThresholdValue()!0, metric.getTitle()!"", metric.getSubtitle()!"", metric.getRange()![], metric.getSteps()![]])}</#assign>
  <#else>
    <#-- Default chart type -->
    <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.pie-chart", [metric.getChartData(), 50])}</#assign>
  </#if>
<#else>
  <#-- Simple value metric - use simple value display template -->
  <#assign metricContent>${tc.includeArgs("tpl.metrics.charts.simple-value", [metric.getValue()!"N/A", metric.getUnit()!"", metric.getLabel()!"", metric.getColor()!"#2563eb", metric.getTrend()!""])}</#assign>
</#if>

<#-- Render the metric using the metric-wrapper template -->
${tc.includeArgs("tpl.metrics.metric-wrapper", [
  metric.getDisplayName()!metric.getName(),
  metricContent,
  "40%",
  "200px",
  metric.getDescription()!""
])}
