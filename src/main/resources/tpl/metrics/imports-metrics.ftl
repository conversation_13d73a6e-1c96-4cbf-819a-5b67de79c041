<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "metrics")}

import mc.fenix.arrange.GemCard;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemGrid;
import mc.fenix.basic.GemText;
import mc.fenix.basic.GemButton;
import mc.fenix.navigation.GemNavItem;

import charts.GemPieChart;
import charts.GemPieChartTypes.GemPieChartData;
import charts.GemPieChartTypes.GemPieChartEntry;
import charts.GemBarChart;
import charts.GemBarChartTypes.GemBarChartData;
import charts.GemLineChart;
import charts.LineChartTypes.GemLineChartData;
import charts.LineChartTypes.GemLineChartEntry;
import charts.GemGaugeChart;
import charts.GemGaugeChartTypes.GemGaugeChartData;
import charts.GemBulletChart;

