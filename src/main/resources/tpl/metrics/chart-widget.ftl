<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#-- Chart widget template - routes to appropriate chart type based on visualization hint -->
<#-- This template follows the interface specification for chart component mapping -->

<#if attributeMetric.visualizationHint?? && attributeMetric.visualizationHint.recommendedChart??>
  <#assign chartType = attributeMetric.visualizationHint.recommendedChart.name()>
  
  <#-- Route to appropriate chart component based on recommended chart type -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.innerRadius!50
      ])}
      <#break>
      
    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.width!"100%", 
        attributeMetric.visualizationHint.templateData.height!"200px"
      ])}
      <#break>
      
    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.width!"100%", 
        attributeMetric.visualizationHint.templateData.height!"200px"
      ])}
      <#break>
      
    <#case "GAUGE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [
        attributeMetric.visualizationHint.templateData.data!{}, 
        attributeMetric.visualizationHint.templateData.min!0, 
        attributeMetric.visualizationHint.templateData.max!100, 
        attributeMetric.visualizationHint.templateData.unit!"", 
        attributeMetric.visualizationHint.templateData.textValue!""
      ])}
      <#break>
      
    <#case "BULLET_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [
        attributeMetric.visualizationHint.templateData.value!0, 
        attributeMetric.visualizationHint.templateData.reference!0, 
        attributeMetric.visualizationHint.templateData.thresholdValue!0, 
        attributeMetric.visualizationHint.templateData.title!"", 
        attributeMetric.visualizationHint.templateData.subtitle!"", 
        attributeMetric.visualizationHint.templateData.range![], 
        attributeMetric.visualizationHint.templateData.steps![]
      ])}
      <#break>
      
    <#case "DATA_TABLE">
      ${tc.includeArgs("tpl.metrics.charts.enhanced-table-component", [domainClass, name, attributeMetric])}
      <#break>
      
    <#default>
      <#-- Default to simple text display for unsupported chart types -->
      ${tc.includeArgs("tpl.metrics.charts.simple-value", [
        attributeMetric.metadata.value!"N/A", 
        attributeMetric.metadata.unit!"", 
        attributeMetric.attributeName, 
        attributeMetric.metadata.color!"#6b7280", 
        ""
      ])}
  </#switch>
<#else>
  <#-- No visualization hint available - use simple text display -->
  ${tc.includeArgs("tpl.metrics.charts.simple-value", [
    attributeMetric.metadata.value!"N/A", 
    attributeMetric.metadata.unit!"", 
    attributeMetric.attributeName, 
    "#9ca3af", 
    ""
  ])}
</#if>
