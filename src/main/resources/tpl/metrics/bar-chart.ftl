${tc.signature(data, width, height)}

<#--
bar-chart.ftl for metrics
Wrapper around GemBarChart specifically for metrics display.

Parameters
- data   : GemBarChartTypes.GemBarChartData  → Prepared data object containing the bars.
- width  : String (optional)                 → Chart width (e.g., "100%")
- height : String (optional)                 → Chart height (e.g., "200px")

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.bar-chart", [data, "100%", "180px"])}
-->

@GemBarChart(
    data = ${data}
    <#if width??>,width = "${width}"</#if>
    <#if height??>,height = "${height}"</#if>
);
