${tc.signature(data, xLabel, yLabel, timestampdata)}

<#--
heatmap-chart.ftl for metrics
Wrapper around GemHeatmapChart following the Chart Components.txt specification.

Parameters
- data          : GemHeatmapChartData → Prepared data object containing the heatmap data.
- xLabel        : String (optional)   → Label for X axis.
- yLabel        : String (optional)   → Label for Y axis.
- timestampdata : Boolean (optional)  → Whether data contains timestamp information.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.heatmap-chart", [data, "X Axis", "Y Axis", false])}
-->

@GemHeatmapChart(
    data = ${data}
    <#if xLabel??>,xLabel = "${xLabel}"</#if>
    <#if yLabel??>,yLabel = "${yLabel}"</#if>
    <#if timestampdata??>,timestampdata = ${timestampdata}</#if>
);
