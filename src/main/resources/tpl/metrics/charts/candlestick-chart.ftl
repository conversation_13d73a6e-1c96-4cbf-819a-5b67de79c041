${tc.signature(data)}

<#--
candlestick-chart.ftl for metrics
Wrapper around GemCandlestickChart following the Chart Components.txt specification.

Parameters
- data : List<GemData2CandlestickChart> → Prepared data object containing the candlestick data.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.candlestick-chart", [data])}
-->

@GemCandlestick<PERSON>hart(
    data = ${data}
);
