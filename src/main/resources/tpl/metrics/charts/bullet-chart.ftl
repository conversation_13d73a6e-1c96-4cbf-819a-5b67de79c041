${tc.signature(value, reference, thresholdvalue, title, subtitle, range, steps)}

<#--
bullet-chart.ftl for metrics
Wrapper around GemBulletChart following the Chart Components.txt specification.

Parameters
- value          : Integer → Primary measure value
- reference      : Integer → Reference value to compare against
- thresholdvalue : Integer → Threshold value for performance indication
- title          : String  → Chart title
- subtitle       : String  → Chart subtitle
- range          : List<Integer> → Qualitative ranges (e.g., poor, satisfactory, good)
- steps          : List<Integer> → Step values for the chart

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.bullet-chart", [75, 80, 90, "Sales Performance", "Q4 2023", [50, 75, 100], [25, 50, 75, 100]])}
-->

@GemBulletChart(
    value = ${value},
    reference = ${reference},
    thresholdvalue = ${thresholdvalue},
    title = "${title}",
    subtitle = "${subtitle}",
    range = ${range},
    steps = ${steps}
);
