${tc.signature(data, dataShort, colors, ignoreError)}

<#--
sunburst-chart.ftl for metrics
Wrapper around GemSunburstChart following the Chart Components.txt specification.

Parameters
- data        : GemSunburstData     → Prepared data object containing the sunburst data.
- dataShort   : GemSDNshort         → Short data representation.
- colors      : List<String> (optional) → List of colors for the chart.
- ignoreError : <PERSON><PERSON><PERSON> (optional)  → Whether to ignore errors during rendering.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.sunburst-chart", [data, dataShort, [], true])}
-->

@GemSunburstChart(
    data = ${data},
    dataShort = ${dataShort}
    <#if colors??>,colors = ${colors}</#if>
    <#if ignoreError??>,ignoreError = ${ignoreError}</#if>
);
