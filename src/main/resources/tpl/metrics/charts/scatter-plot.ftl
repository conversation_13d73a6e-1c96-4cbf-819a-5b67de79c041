${tc.signature(data, xAxis, yAxis)}

<#--
scatter-plot.ftl for metrics
Wrapper around GemScatterPlot following the Chart Components.txt specification.

Parameters
- data  : GemScatterPlotData → Prepared data object containing the scatter plot data.
- xAxis : GemScatterPlotAxis → X axis configuration.
- yAxis : GemScatterPlotAxis → Y axis configuration.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [data, xAxis, yAxis])}
-->

@GemScatterPlot(
    data = ${data},
    xAxis = ${xAxis},
    yAxis = ${yAxis}
);
