${tc.signature(data, innerRadius)}

<#--
pie-chart.ftl for metrics
Wrapper around Gem<PERSON>ie<PERSON>hart following the Chart Components.txt specification.

Parameters
- data        : GemPieChartData  → Prepared data object containing the slices.
- innerRadius : Integer (optional) → Size of the doughnut hole; default = 50.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.pie-chart", [data, 40])}
-->

<#-- Fallback for optional argument -->
<#assign _inner = innerRadius?default(50)>

@GemPieChart(
    data = ${data},
    innerRadius = ${_inner}
);
