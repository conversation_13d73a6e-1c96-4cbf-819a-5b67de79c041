${tc.signature(value, unit, label, color, trend)}

<#--
simple-value.ftl for metrics
Simple numeric display for metrics without using chart components.

Parameters
- value : String/Number             → Value to display
- unit  : String (optional)         → Unit to display (e.g., "%", "MB", "users")
- label : String (optional)         → Label text below the value
- color : String (optional)         → Color for the value (default: "#2563eb")
- trend : String (optional)         → Trend indicator: "up", "down", "neutral"

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.simple-value", [1250, "users", "Active Users", "#10b981", "up"])}
-->

<#-- Fallbacks for optional arguments -->
<#assign _unit = unit?default("")>
<#assign _label = label?default("")>
<#assign _color = color?default("#2563eb")>
<#assign _trend = trend?default("")>

@GemColumn(
  hAlign = "center",
  vAlign = "center",
  rowGap = "5px",
  components = [
    @GemRow(
      hAlign = "center",
      vAlign = "center",
      columnGap = "5px",
      components = [
        @GemText(
          value = "${value}",
          fontSize = "36px",
          fontWeight = "bold",
          color = "${_color}"
        )<#if _unit != "">,
        @GemText(
          value = "${_unit}",
          fontSize = "18px",
          color = "#6b7280"
        )</#if><#if _trend != "">,
        @GemText(
          value = "<#if _trend == "up">↗<#elseif _trend == "down">↘<#else>→</#if>",
          fontSize = "24px",
          color = "<#if _trend == "up">#10b981<#elseif _trend == "down">#ef4444<#else>#6b7280</#if>"
        )</#if>
      ]
    )<#if _label != "">,
    @GemText(
      value = "${_label}",
      fontSize = "14px",
      color = "#6b7280",
      textAlign = "center"
    )</#if>
  ]
);
