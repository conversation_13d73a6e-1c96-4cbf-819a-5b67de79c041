${tc.signature(data, stacked, maxValue, minValue)}

<#--
bar-chart.ftl for metrics
Wrapper around GemBarChart following the Chart Components.txt specification.

Parameters
- data     : GemBarChartData  → Prepared data object containing the bars.
- stacked  : <PERSON><PERSON><PERSON> (optional) → Whether bars should be stacked; default = false.
- maxValue : Integer (optional) → Maximum value for the chart scale.
- minValue : Integer (optional) → Minimum value for the chart scale.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.bar-chart", [data, true, 100, 0])}
-->

@GemBarChart(
    data = ${data}
    <#if stacked??>,stacked = ${stacked}</#if>
    <#if maxValue??>,maxValue = ${maxValue}</#if>
    <#if minValue??>,minValue = ${minValue}</#if>
);
