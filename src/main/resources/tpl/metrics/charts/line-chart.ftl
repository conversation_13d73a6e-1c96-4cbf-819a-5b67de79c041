${tc.signature(data, enableBackgroundColor, maxValue, minValue)}

<#--
line-chart.ftl for metrics
Wrapper around GemLine<PERSON>hart following the Chart Components.txt specification.

Parameters
- data                  : GemLineChartData  → Prepared data object containing the lines.
- enableBackgroundColor : <PERSON><PERSON><PERSON> (optional) → Whether to enable background color; default = false.
- maxValue             : Integer (optional) → Maximum value for the chart scale.
- minValue             : Integer (optional) → Minimum value for the chart scale.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.line-chart", [data, true, 100, 0])}
-->

@GemLineChart(
    data = ${data}
    <#if enableBackgroundColor??>,enableBackgroundColor = ${enableBackgroundColor}</#if>
    <#if maxValue??>,maxValue = ${maxValue}</#if>
    <#if minValue??>,minValue = ${minValue}</#if>
);
