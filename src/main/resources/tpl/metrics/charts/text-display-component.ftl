${tc.signature("domainClass", "name", "attributeMetric")}

<#--
text-display-component.ftl for metrics
Text display component for attributes that don't have visualization hints or are not suitable for charts.
This component provides a clean text-based representation of metric information.

Parameters
- domainClass     : ASTCDClass        → The domain class
- name           : String            → Class name
- attributeMetric : AttributeMetric   → The attribute metric data

-->

@GemColumn(
  hAlign = "center",
  vAlign = "center",
  rowGap = "10px",
  components = [

    <#-- Attribute name as main display -->
    @GemText(value = "${attributeMetric.attributeName}"),

    <#-- Data type information -->
    <#if attributeMetric.dataType??>
    @GemText(value = "Type: ${attributeMetric.dataType.displayName}"),
    </#if>

    <#-- Metric scale information -->
    <#if attributeMetric.scale??>
    @GemText(value = "Scale: ${attributeMetric.scale.displayName}"),
    </#if>

    <#-- Confidence level -->
    <#if attributeMetric.confidence??>
    @GemText(value = "Confidence: ${attributeMetric.confidence?string('0.00')}"),
    </#if>

    <#-- Status message -->
    @GemText(value = <#if attributeMetric.visualizationHint??>"No suitable chart type available"<#else>"No visualization hint available"</#if>),

    <#-- Additional metadata if available -->
    <#if attributeMetric.metadata?? && attributeMetric.metadata.value??>
    @GemText(value = "Value: ${attributeMetric.metadata.value} ${attributeMetric.metadata.unit!''}")
    </#if>
  ]
);
