${tc.signature("domainClass", "name", "attributeMetric")}

<#--
text-display-component.ftl for metrics
Text display component for attributes that don't have visualization hints or are not suitable for charts.
This component provides a clean text-based representation of metric information.

Parameters
- domainClass     : ASTCDClass        → The domain class
- name           : String            → Class name
- attributeMetric : AttributeMetric   → The attribute metric data

Usage in chart-widget:
  ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
-->

@GemColumn(
  hAlign = "center",
  vAlign = "center",
  rowGap = "10px",
  components = [
    
    <#-- Attribute name as main display -->
    @GemText(
      value = "${attributeMetric.attributeName}",
      fontSize = "24px",
      fontWeight = "bold",
      color = "#374151",
      textAlign = "center"
    ),
    
    <#-- Data type information -->
    <#if attributeMetric.dataType??>
    @GemText(
      value = "Type: ${attributeMetric.dataType.displayName}",
      fontSize = "14px",
      color = "#6b7280",
      textAlign = "center"
    ),
    </#if>
    
    <#-- Metric scale information -->
    <#if attributeMetric.scale??>
    @GemText(
      value = "Scale: ${attributeMetric.scale.displayName}",
      fontSize = "14px",
      color = "#6b7280",
      textAlign = "center"
    ),
    </#if>
    
    <#-- Confidence level -->
    <#if attributeMetric.confidence??>
    @GemText(
      value = "Confidence: ${attributeMetric.confidence?string('0.00')}",
      fontSize = "14px",
      color = <#if (attributeMetric.confidence > 0.8)>"#059669"<#elseif (attributeMetric.confidence > 0.5)>"#d97706"<#else>"#dc2626"</#if>,
      textAlign = "center"
    ),
    </#if>
    
    <#-- Status message -->
    @GemText(
      value = <#if attributeMetric.visualizationHint??>"No suitable chart type available"<#else>"No visualization hint available"</#if>,
      fontSize = "12px",
      color = "#9ca3af",
      textAlign = "center",
      fontStyle = "italic"
    ),
    
    <#-- Additional metadata if available -->
    <#if attributeMetric.metadata?? && attributeMetric.metadata.value??>
    @GemText(
      value = "Value: ${attributeMetric.metadata.value} ${attributeMetric.metadata.unit!''}",
      fontSize = "16px",
      color = "#4b5563",
      textAlign = "center",
      fontWeight = "medium"
    )
    </#if>
  ]
);
