${tc.signature(data, min, max, unit, textValue)}

<#--
gauge-chart.ftl for metrics
Wrapper around GemGauge<PERSON>hart following the Chart Components.txt specification.

Parameters
- data      : GemGaugeChartData → Prepared data object for the gauge.
- min       : Integer (optional) → Minimum value for the gauge scale.
- max       : Integer (optional) → Maximum value for the gauge scale.
- unit      : String (optional)  → Unit to display (e.g., "kt", "%").
- textValue : String (optional)  → Text value to display on the gauge.

Usage in metrics templates:
  ${tc.includeArgs("tpl.metrics.charts.gauge-chart", [data, -30, 70, "kt", "CO2 Emission"])}
-->

@GemGaugeChart(
    data = ${data}
    <#if min??>,min = ${min}</#if>
    <#if max??>,max = ${max}</#if>
    <#if unit??>,unit = "${unit}"</#if>
    <#if textValue??>,textValue = "${textValue}"</#if>
);
