<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#-- Enhanced table component for DATA_TABLE visualization type -->
<#-- This component displays metric data in a structured table format -->

${name?uncap_first}_${attributeMetric.attributeName}Table@GemColumn(
  rowGap = "5px",
  components = [
    
    <#-- Table header -->
    ${name?uncap_first}_${attributeMetric.attributeName}TableHeader@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Property", fontWeight = "bold", fontSize = "14px")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "Value", fontWeight = "bold", fontSize = "14px")
        ])
      ]
    ),
    
    <#-- Attribute name row -->
    ${name?uncap_first}_${attributeMetric.attributeName}NameRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Attribute Name", fontSize = "12px", color = "#6b7280")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.attributeName}", fontSize = "12px")
        ])
      ]
    ),
    
    <#-- Data type row -->
    <#if attributeMetric.dataType??>
    ${name?uncap_first}_${attributeMetric.attributeName}DataTypeRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Data Type", fontSize = "12px", color = "#6b7280")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.dataType.displayName}", fontSize = "12px")
        ])
      ]
    ),
    </#if>
    
    <#-- Metric scale row -->
    <#if attributeMetric.scale??>
    ${name?uncap_first}_${attributeMetric.attributeName}ScaleRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Metric Scale", fontSize = "12px", color = "#6b7280")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.scale.displayName}", fontSize = "12px")
        ])
      ]
    ),
    </#if>
    
    <#-- Confidence row -->
    <#if attributeMetric.confidence??>
    ${name?uncap_first}_${attributeMetric.attributeName}ConfidenceRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Confidence", fontSize = "12px", color = "#6b7280")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(
            value = "${attributeMetric.confidence?string('0.00')}", 
            fontSize = "12px",
            color = <#if (attributeMetric.confidence > 0.8)>"#059669"<#elseif (attributeMetric.confidence > 0.5)>"#d97706"<#else>"#dc2626"</#if>
          )
        ])
      ]
    ),
    </#if>
    
    <#-- Additional metadata rows -->
    <#if attributeMetric.metadata?? && attributeMetric.metadata?has_content>
      <#list attributeMetric.metadata?keys as key>
        <#if key != "value" && key != "unit" && key != "color" && key != "trend">
        ${name?uncap_first}_${attributeMetric.attributeName}_${key}Row@GemRow(
          hAlign = "space-between",
          components = [
            @GemColumn(width = "30%", components = [
              @GemText(value = "${key?cap_first}", fontSize = "12px", color = "#6b7280")
            ]),
            @GemColumn(width = "70%", components = [
              @GemText(value = "${attributeMetric.metadata[key]!''}", fontSize = "12px")
            ])
          ]
        )<#sep>,</#sep>
        </#if>
      </#list>
    </#if>
  ]
)
