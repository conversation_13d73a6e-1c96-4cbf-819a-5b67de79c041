<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#-- Enhanced table component for DATA_TABLE visualization type -->
<#-- This component displays metric data in a structured table format -->

${name?uncap_first}_${attributeMetric.attributeName}Table@GemColumn(
  rowGap = "5px",
  components = [
    
    <#-- Table header -->
    ${name?uncap_first}_${attributeMetric.attributeName}TableHeader@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Property")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "Value")
        ])
      ]
    ),
    
    <#-- Attribute name row -->
    ${name?uncap_first}_${attributeMetric.attributeName}NameRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Attribute Name")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.attributeName}")
        ])
      ]
    ),
    
    <#-- Data type row -->
    <#if attributeMetric.dataType??>
    ${name?uncap_first}_${attributeMetric.attributeName}DataTypeRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Data Type")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.dataType.displayName}")
        ])
      ]
    ),
    </#if>
    
    <#-- Metric scale row -->
    <#if attributeMetric.scale??>
    ${name?uncap_first}_${attributeMetric.attributeName}ScaleRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Metric Scale")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.scale.displayName}")
        ])
      ]
    ),
    </#if>
    
    <#-- Confidence row -->
    <#if attributeMetric.confidence??>
    ${name?uncap_first}_${attributeMetric.attributeName}ConfidenceRow@GemRow(
      hAlign = "space-between",
      components = [
        @GemColumn(width = "30%", components = [
          @GemText(value = "Confidence")
        ]),
        @GemColumn(width = "70%", components = [
          @GemText(value = "${attributeMetric.confidence?string('0.00')}")
        ])
      ]
    ),
    </#if>
    
    <#-- Additional metadata rows -->
    <#if attributeMetric.metadata?? && attributeMetric.metadata?has_content>
      <#list attributeMetric.metadata?keys as key>
        <#if key != "value" && key != "unit" && key != "color" && key != "trend">
        ${name?uncap_first}_${attributeMetric.attributeName}_${key}Row@GemRow(
          hAlign = "space-between",
          components = [
            @GemColumn(width = "30%", components = [
              @GemText(value = "${key?cap_first}")
            ]),
            @GemColumn(width = "70%", components = [
              @GemText(value = "${attributeMetric.metadata[key]!''}")
            ])
          ]
        )<#sep>,</#sep>
        </#if>
      </#list>
    </#if>
  ]
)
