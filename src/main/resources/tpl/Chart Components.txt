BarChart
Usage example
GemBarChartExample takes GemBarChart in its body.

package example;

import charts.GemBarChart;
import charts.GemBarChartTypes.GemBarChartData;

page GemBarChartExample(
  GemBarChartData data, 
  Integer maxValue, 
  Integer minValue
) {
  @GemBarChart(
    data = data,
    stacked = true,
    maxValue = maxValue,
    minValue = minValue
  ); 
}

GUI Model
GemBarChart.gui (open)

package charts;

import charts.GemBarChartTypes.GemBarChartData;

//A Bar-Chart present a set of two dimensional data with a number of rectangular bars that can go //horizontally or vertically, with their values being proportional to what they represent.
//The chart itself visualizes categories (given as Strings) on one axis and a discrete integer value on //the other as height or size.
//Additionally, the bars can be stacked or aligned to visualizes multiple sub-categories of a second data //dimension.

component GemBarChart(
  GemBarChartData data,
  Object displayValue,
  Boolean stacked,
  Integer maxValue,
  Integer minValue
)
=====
BulletChart

Usage example
GemBulletChartExample utilizes GemBulletChart in its body.

package example;

import charts.GemBulletChart;

page GemBulletChartExample(
  Integer value,
  Integer reference,
  Integer thresholdvalue,
  String title,
  String subtitle,
  List<Integer> range,
  List<Integer> steps
) {
  @GemBulletChart(
    value = value,
    reference = reference,
    thresholdvalue = thresholdvalue,
    title = title,    
    subtitle = subtitle,
    range = range,
    steps = steps
  );
}

GUI Model
GemBulletChart.gui (open)

package charts;

//Bullet chart is the variation of the bar chart, it features a //single, primary measure,
//compares that measure to one or more other measures to enrich its //meaning (for example, compared to a target),
//and displays it in the context of qualitative ranges of performance, //such as poor, satisfactory, and good.

component GemBulletChart(
  Integer value,
  Integer reference,
  Integer thresholdvalue,
  String title,
  String subtitle,
  List<Integer> range,
  List<Integer> steps
)
=====
CandlestickChart
Usage example
GemCandlestickChartExample takes GemCandlestickChart in its body.

package example;

import charts.GemCandlestickChart.gui;
import charts.GemCandlestickChartTypes.GemData2CandlestickChart;

page GemCandlestickChartExample(
  GemData2CandlestickChart data,
) {
  @GemCandlestickChart(
    data = data
  );
}
GUI Model
GemCandlestickChart.gui (open)

package charts;

import charts.GemCandlestickChartTypes.GemData2CandlestickChart;

//A Candlestick chart displays a few important datapoints for a (short) period of time.
//It is often used to display how stock prices changed over said short periods.
//The data of a candlestick is displayed by its horizontal position (time), vertical position of: start //and end of the bar and start and end of the line that crosses through the bar (price of stock).
//The color of the candlestick indicates if the price got higher (green) or lower (red), which is an //important information because it dictates if the upper bar end shows the first price traded during that //time period (if the price got lower) or the last price traded (if the price got higher).
//And the other way around for the bottom end position of the bar.

component GemCandlestickChart(
  List<GemData2CandlestickChart> data
)
=====
GaugeChart

Usage example
GemGaugeChartExample takes GemGaugeChart in its body

package example_charts;

import charts.GemGaugeChart;
import charts.GemGaugeChartTypes.GemGaugeChartData;

page GemGaugeChartExample(
  GemGaugeChartData data
) {
  @GemGaugeChart(
    data = data,
    min = -30,
    max = 70,
    unit = "kt",
    textValue = "CO2 Emission"
  );
}

GUI Model
GemGaugeChart.gui (open)

package charts;

import charts.GemGaugeChartTypes.GemGaugeChartData;

//Gauge charts are also known as dial charts or speedometer charts. 
//Normally they consist of a dial and a pointer, use the pointer to 
//read the information. There are several different types of gauge 
//charts, such as Speedometer, Linear Gauge Charts and Angular Gauge 
//Charts. Gauge charts are very helpful to visualize and compare 
//single data value with a quantitative context.

component GemGaugeChart (
  GemGaugeChartData data,
  Integer min,
  Integer max,
  String unit,
  String textValue
)
=====
HeatmapChart

Usage example
GemHeatmapChartExample takes GemHeatmapChart in its body

package example;

import charts.GemHeatmapChart;
import charts.GemHeatmapChartTypes.GemHeatmapChartData;

page GemHeatmapChartExample(
  GemHeatmapChartData data
) {
  @GemHeatmapChart(
    data = data
  );
}

GUI Model
GemHeatmapChart.gui (open)

package charts;

import charts.GemHeatmapChartTypes.GemHeatmapChartData;

//Heatmap-charts visualize different kinds of data, for instance temperature, in a tabular-like format.
//They are helpful for finding patterns, relationships between data or other kind of information.

component GemHeatmapChart (
  GemHeatmapChartData data,
  String xLabel,
  String yLabel,
  Boolean timestampdata
)
=====

LineChart

Usage example
GemLineChartExample takes GemLineChart in its body

package example;

import charts.GemLineChart;
import charts.LineChartTypes.GemLineChartData;
import charts.LineChartTypes.GemLineChartEntry;

page GemLineChartExample(
  GemLineChartData data,
  GemLineChartEntry entry
) {
  @GemLineChart(
    data = data,
    entry = entry
  );
}

GUI Model
GemLineChart.gui (open)

package charts;

import charts.GemLineChartTypes.GemLineChartData;

//Line chart is a graphical representation to describe the data changes over a given period. It is //connected by straight line segments.
//The horizontal axis is usually a time scale; for example, minutes, hours, days, months, or years, while //the vertical axis would have the values.

component GemLineChart(
  GemLineChartData data,
  Object displayValue,
  Boolean enableBackgroundColor,
  Integer maxValue,
  Integer minValue
)
=====
PieChart

Usage example
GemPieChartExample takes GemPieChart in its body

package example;

import charts.GemPieChart;
import charts.GemPieChartTypes.GemPieChartData;
import charts.GemPieChartTypes.GemPieChartEntry;

page GemPieChartExample(
  GemPieChartData data
) {
  @GemPieChart(
    data = data,
    innerRadius = 50
  );
}

GUI Model
GemPieChart.gui (open)

package charts;

import charts.GemPieChartTypes.GemPieChartData;

//Pie chart is also called circle chart. It looks like a circle, which is divided to slices to visualize //information.
//Different categories of data could be indicated by different colors.
//It is helpful to observe the numerical proportion of data.

component GemPieChart(
    GemPieChartData data,
    Integer innerRadius
)
=====
SunburstChart

Usage example
GemSunburstChartExample takes GemSunburstChart in its body

package example;

import charts.GemSunburstChart;
import charts.GemSunburstChartTypes.GemSunburstData;
import charts.GemSunburstChartTypes.GemSDNshort;

page GemSunburstChartExample(
  GemSunburstData data,
  GemSDNshort dataShort
) {
  @GemSunburstChart(
    data = data,
    dataShort = dataShort,
    ignoreError = true
  );
}

GUI Model
GemSunburstChart.gui (open)

package charts;

import charts.GemSunburstChartTypes.GemSunburstData;
import charts.GemSunburstChartTypes.GemSDNshort;

//Sunburst-charts look like pie-charts with multiple layers.
//Each of them refine the information of the previous layer and give information about the distribution of //the parent layer in percent.
//The sunburst-chart can also be represented by a tree-like structure with parent nodes and subnodes which //contain the refined information about the parent nodes.

component GemSunburstChart (
  GemSunburstData data,
  GemSDNshort dataShort,
  List<String> colors,
  Boolean ignoreError
)
=====
ScatterPlot

Usage example
Given gui model:

package example_charts;

import Domain.Person;
import mc.fenix.charts.GemScatterPlot;
import mc.fenix.charts.GemScatterPlotAxis;

page GemScatterPlotExample(List<Person> persons) {
  simple@GemScatterPlot(
    data = null,
    xAxis = null,
    yAxis = null
  );
}

GUI Model
GemScatterPlot.gui(open)

package mc.fenix.charts;

import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotData;
import mc.fenix.charts.GemScatterPlotTypes.GemScatterPlotAxis;

component GemScatterPlot(
  GemScatterPlotData data,
  GemScatterPlotAxis xAxis,
  GemScatterPlotAxis yAxis
)